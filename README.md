# 葡萄酒质量预测项目

## 项目简介
本项目使用四种不同的回归模型来预测葡萄酒质量，包括：
1. **线性回归 (Linear Regression)**
2. **决策树回归 (Decision Tree Regressor)**
3. **随机森林回归 (Random Forest Regressor)**
4. **多层感知机回归 (MLP Regressor)**

## 数据集
- 使用UCI机器学习库中的红酒质量数据集
- 包含11个特征变量和1个目标变量（质量评分）
- 数据集大小：1599个样本

### 特征变量
- fixed acidity（固定酸度）
- volatile acidity（挥发性酸度）
- citric acid（柠檬酸）
- residual sugar（残糖）
- chlorides（氯化物）
- free sulfur dioxide（游离二氧化硫）
- total sulfur dioxide（总二氧化硫）
- density（密度）
- pH（酸碱度）
- sulphates（硫酸盐）
- alcohol（酒精度）

### 目标变量
- quality（质量评分，范围3-8）

## 项目结构
```
├── wine_quality_prediction.py  # 主程序文件
├── requirements.txt            # 依赖包列表
├── model_comparison.png        # 模型预测结果对比图
├── metrics_comparison.png      # 模型性能指标对比图
└── README.md                  # 项目说明文档
```

## 安装和运行

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 运行程序
```bash
python wine_quality_prediction.py
```

## 模型性能结果

根据最新运行结果：

| 模型 | MSE | RMSE | MAE | R² |
|------|-----|------|-----|-----|
| Linear Regression | 0.3900 | 0.6245 | 0.5035 | 0.4032 |
| Decision Tree | 0.5804 | 0.7618 | 0.5225 | 0.1119 |
| Random Forest | 0.3182 | 0.5641 | 0.4447 | 0.5131 |
| MLP Regressor | 0.4703 | 0.6858 | 0.5180 | 0.2803 |

**最佳模型：Random Forest（随机森林）**
- 具有最低的RMSE (0.5641)
- 最高的R²值 (0.5131)
- 在所有评估指标上表现最佳

## 功能特点

1. **数据加载和探索**
   - 自动从UCI数据库下载数据
   - 如果网络不可用，会生成示例数据
   - 提供详细的数据统计信息

2. **数据预处理**
   - 特征标准化
   - 训练集/测试集划分（8:2）

3. **模型训练**
   - 四种不同的回归算法
   - 针对不同模型使用适当的数据预处理

4. **模型评估**
   - 多种评估指标：MSE、RMSE、MAE、R²
   - 自动选择最佳模型

5. **结果可视化**
   - 真实值vs预测值散点图
   - 模型性能指标对比图
   - 支持中文显示

## 技术栈
- Python 3.x
- pandas：数据处理
- numpy：数值计算
- scikit-learn：机器学习算法
- matplotlib：数据可视化
- seaborn：统计图表

## 注意事项
- 程序会自动生成两个PNG图片文件用于结果展示
- 如果遇到中文字体显示问题，可能需要安装SimHei字体
- 建议在Python 3.7+环境中运行
